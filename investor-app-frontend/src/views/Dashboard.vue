<template>
  <div class="dashboard-container container container-fluid py-4">
    <!-- Dashboard Walkthrough Component -->
    <DashboardWalkthrough />
    <!-- Welcome Header -->
    <div class="dashboard-header d-flex flex-column flex-md-row justify-content-between align-items-center mb-4">
      <div>
        <h2 class="fw-600 header-title">Welcome back, {{ accountTitle }}</h2>
        <p class="header-subtitle text-muted">Manage your investments with ease</p>
      </div>
      <button class="btn btn-primary refresh-btn" @click="refreshData">
        <i class="bi bi-arrow-clockwise me-2"></i> Refresh Data
      </button>
    </div>

    <!-- Dashboard Tiles: Row 1 -->
    <div class="row g-4">
      <!-- Combined Assets Balance Summary Tile -->
      <div class="col-lg-6 col-md-6">
        <div class="card dashboard-card asset-balance-tile">
          <div class="card-body">
            <div class="d-flex justify-content-between align-items-center mb-4">
              <h5 class="card-title mb-0">Assets Balance Summary</h5>
              <div class="balance-badge" :class="user?.role === 'manager' ? 'badge-manager' : 'badge-client'">
                {{ user?.role === 'manager' ? 'Manager View' : 'Client View' }}
              </div>
            </div>
            <LoadingState
              :loading="isLoading"
              :empty="!pendingAssetsBalance && !approvedAssetsBalance"
              empty-icon="bi bi-wallet2"
              empty-title="No Assets Found"
              empty-message="You don't have any assets yet. Start by adding your first asset."
            >
              <div class="balance-content">
                <!-- Total Balance -->
                <div class="total-balance-container">
                  <div class="total-balance-card">
                    <div class="total-balance-header">Total Balance</div>
                    <div class="total-balance-value">
                      ${{ formatTotalBalance() }}
                    </div>
                    <button @click="toggleBreakdown" class="breakdown-toggle-btn">
                      <span>{{ showBreakdown ? 'Hide' : 'Show' }} Breakdown</span>
                      <i :class="['bi', showBreakdown ? 'bi-chevron-up' : 'bi-chevron-down']"></i>
                    </button>
                  </div>
                </div>

                <!-- Breakdown (Collapsible) -->
                <Transition name="slide-fade">
                  <div v-if="showBreakdown" class="balance-breakdown-container mt-4">
                    <h6 class="breakdown-title">Balance Breakdown</h6>

                    <div class="balance-categories">
                      <!-- Pending Balance -->
                      <div class="balance-category">
                        <div class="category-header">
                          <div class="category-icon pending-icon">
                            <i class="bi bi-hourglass-split"></i>
                          </div>
                          <div class="category-name">Pending</div>
                        </div>
                        <div class="category-value">
                          ${{ formatCategoryValue(assetCategories.pending_value) }}
                        </div>
                        <div class="category-bar-container">
                          <div class="category-bar pending-bar"
                               :style="{ width: calculatePercentage(assetCategories.pending_value) + '%' }"></div>
                        </div>
                      </div>

                      <!-- Approved Balance -->
                      <div class="balance-category">
                        <div class="category-header">
                          <div class="category-icon approved-icon">
                            <i class="bi bi-check-circle"></i>
                          </div>
                          <div class="category-name">Approved</div>
                        </div>
                        <div class="category-value">
                          ${{ formatCategoryValue(assetCategories.approved_value) }}
                        </div>
                        <div class="category-bar-container">
                          <div class="category-bar approved-bar"
                               :style="{ width: calculatePercentage(assetCategories.approved_value) + '%' }"></div>
                        </div>
                      </div>

                      <!-- Vested Balance -->
                      <div class="balance-category">
                        <div class="category-header">
                          <div class="category-icon vested-icon">
                            <i class="bi bi-calendar-check"></i>
                          </div>
                          <div class="category-name">Vested</div>
                        </div>
                        <div class="category-value">
                          ${{ formatCategoryValue(assetCategories.vested_value) }}
                        </div>
                        <div class="category-bar-container">
                          <div class="category-bar vested-bar"
                               :style="{ width: calculatePercentage(assetCategories.vested_value) + '%' }"></div>
                        </div>
                      </div>

                      <!-- Tokenised Balance -->
                      <div class="balance-category">
                        <div class="category-header">
                          <div class="category-icon tokenised-icon">
                            <i class="bi bi-coin"></i>
                          </div>
                          <div class="category-name">Tokenised</div>
                        </div>
                        <div class="category-value">
                          ${{ formatCategoryValue(assetCategories.tokenised_value) }}
                        </div>
                        <div class="category-bar-container">
                          <div class="category-bar tokenised-bar"
                               :style="{ width: calculatePercentage(assetCategories.tokenised_value) + '%' }"></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </Transition>
              </div>
            </LoadingState>
            <p class="tile-caption text-center text-muted mt-4">
              {{ user?.role === 'manager' ? 'Showing total balances for all managed clients' : 'Showing your individual account balances' }}
            </p>
          </div>
        </div>
      </div>
      <!-- Tokenised Assets Tile -->
      <div class="col-lg-6 col-md-6">
        <div class="card dashboard-card tokenised-assets-tile">
          <div class="card-body">
            <h5 class="card-title mb-3">{{ user?.role === 'manager' ? 'Client Assets Overview' : 'Tokenised Assets' }}</h5>
            <LoadingState
              :loading="isLoading"
              :empty="!topTokenisedAssets || !topTokenisedAssets.length"
              empty-icon="bi bi-coin"
              empty-title="No Tokenised Assets"
              empty-message="You don't have any tokenised assets yet. Start by tokenising your first asset."
            >
              <!-- Manager View: Show client totals -->
              <ul v-if="user?.role === 'manager'" class="list-group w-100 asset-list">
                <li v-for="(asset, index) in topTokenisedAssets" :key="index"
                  class="list-group-item w-100 d-flex justify-content-between align-items-center">
                  <div class="asset-info">
                    <strong class="asset-name">{{ asset.name }}</strong><br>
                    <small class="text-muted">{{ asset.type }} | {{ asset.approval_status }}</small>
                    <small v-if="asset.ownership_records && asset.ownership_records.length" class="d-block text-primary">
                      {{ asset.ownership_records.length }} client{{ asset.ownership_records.length !== 1 ? 's' : '' }}
                    </small>
                  </div>
                  <span class="asset-value fw-600 text-success">
                    ${{ Number(asset.value || 0).toFixed(2) }}
                  </span>
                </li>
              </ul>

              <!-- Client View: Show individual account balances -->
              <ul v-else class="list-group w-100 asset-list">
                <li v-for="(asset, index) in topTokenisedAssets" :key="index"
                  class="list-group-item w-100 d-flex justify-content-between align-items-center">
                  <div class="asset-info">
                    <strong class="asset-name">{{ asset.name }}</strong><br>
                    <small class="text-muted">{{ asset.type }} | {{ asset.approval_status }}</small>
                    <small v-if="asset.blockchain_tx_hash" class="d-block text-primary">
                      <i class="bi bi-link-45deg"></i> On blockchain
                    </small>
                  </div>
                  <span class="asset-value fw-600 text-success">
                    ${{ Number(asset.value || 0).toFixed(2) }}
                  </span>
                </li>
              </ul>
            </LoadingState>
            <div class="d-flex justify-content-between mt-3">
              <RouterLink to="/assets" class="btn btn-primary">
                <i class="bi bi-coin me-2"></i> Manage Assets
              </RouterLink>
              <RouterLink to="/vested-assets" class="btn btn-outline-primary">
                <i class="bi bi-calendar-check me-2"></i> Vested Assets
              </RouterLink>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Balance History Chart Row -->
    <div class="row g-4 mt-0">
      <div class="col-12">
        <BalanceHistoryTile
          :balance-history-data="balanceHistoryData"
          :is-loading="isLoadingBalanceHistory"
          :client-count="effectiveClientCount"
        />
      </div>
    </div>

    <!-- Dashboard Tiles: Row 2 -->
    <div class="row g-4 mt-4">
      <!-- Profile Overview Tile -->
      <div class="col-lg-6 col-md-6">
        <div class="card dashboard-card profile-tile">
          <div class="card-body">
            <h5 class="card-title mb-3">Your Profile</h5>
            <div class="profile-info mb-3">
              <img :src="userAvatar" alt="Profile" class="profile-avatar">
              <div class="profile-details">
                <p class="profile-name mb-1">{{ user?.name || user?.contact_name || 'N/A' }}</p>
                <p class="profile-email text-muted mb-0">{{ user?.email || 'N/A' }}</p>
              </div>
            </div>
            <div class="membership-info mb-3">
              <strong>Membership:</strong>
              <span class="text-muted">{{ user?.membership || 'N/A' }}</span>
            </div>
            <RouterLink to="/settings/profile" class="btn btn-primary btn-block">
              <i class="bi bi-person-circle me-2"></i> Manage Profile
            </RouterLink>
          </div>
        </div>
      </div>

      <!-- Recent Activity Tile -->
      <div class="col-lg-6 col-md-6">
        <div class="card dashboard-card recent-activity-tile">
          <div class="card-body">
            <div class="d-flex justify-content-between align-items-center mb-3">
              <h5 class="card-title mb-0">Recent Activity</h5>
              <button v-if="activities.length > 5"
                      class="btn btn-link text-primary p-0 d-flex align-items-center"
                      @click="showAllActivities">
                View All <i class="bi bi-arrow-right ms-1"></i>
              </button>
            </div>
            <div v-if="isLoadingActivities" class="text-center my-3">
              <div class="spinner-border text-primary spinner-lg" role="status">
                <span class="visually-hidden">Loading...</span>
              </div>
              <p class="mt-2 text-muted">Fetching latest activity data...</p>
            </div>
            <div v-else class="activity-list">
              <div v-if="recentActivities.length === 0" class="text-center text-muted">
                No recent activity to display
              </div>
              <div v-else v-for="activity in recentActivities" :key="activity.id" class="activity-item">
                <div class="d-flex align-items-center">
                  <div class="activity-icon me-3" :class="getActivityColorClass(activity.type)">
                    <i :class="['bi', getActivityIcon(activity.type)]"></i>
                  </div>
                  <div class="activity-content flex-grow-1">
                    <div class="d-flex justify-content-between align-items-start">
                      <div>
                        <p class="mb-1 activity-title">{{ formatActivityTitle(activity) }}</p>
                        <p class="mb-0 activity-details" v-if="activity.description">{{ activity.description }}</p>
                        <small class="text-muted d-block mt-1" v-if="activity.metadata?.asset_value">
                          Value: ${{ Number(activity.metadata.asset_value).toLocaleString() }}
                        </small>
                      </div>
                      <span class="activity-time ms-2">{{ formatActivityDate(activity.timestamp || activity.created_at) }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- All Activities Modal -->
      <Transition name="backdrop">
        <div v-show="showActivitiesModal" class="modal-backdrop"></div>
      </Transition>

      <Transition name="modal-inner">
        <div v-show="showActivitiesModal" class="modal-wrapper">
          <div class="modal-dialog modal-lg">
            <div class="modal-content">
              <div class="modal-header">
                <h5 class="modal-title">
                  <i class="bi bi-clock-history me-2"></i>
                  Activity History
                </h5>
                <button type="button" class="modal-close-btn" @click="closeActivitiesModal">
                  <i class="bi bi-x-lg"></i>
                </button>
              </div>
              <div class="modal-body">
                <div v-if="activities.length === 0" class="text-center text-muted py-4">
                  No activities to display
                </div>
                <div v-else class="activity-list">
                  <div v-for="activity in activities" :key="activity.id" class="activity-item">
                    <div class="d-flex align-items-center">
                      <div class="activity-icon me-3" :class="getActivityColorClass(activity.type)">
                        <i :class="['bi', getActivityIcon(activity.type)]"></i>
                      </div>
                      <div class="activity-content flex-grow-1">
                        <div class="d-flex justify-content-between align-items-start">
                          <div>
                            <p class="mb-1 activity-title">{{ formatActivityTitle(activity) }}</p>
                            <p class="mb-0 activity-details" v-if="activity.description">{{ activity.description }}</p>
                            <small class="text-muted d-block mt-1" v-if="activity.metadata?.asset_value">
                              Value: ${{ Number(activity.metadata.asset_value).toLocaleString() }}
                            </small>
                            <small class="text-muted d-block" v-if="activity.user">
                              By: {{ activity.user.contact_name }}
                            </small>
                          </div>
                          <span class="activity-time ms-2">{{ formatActivityDate(activity.timestamp || activity.created_at) }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="modal-footer">
                <button type="button" class="btn btn-secondary" @click="closeActivitiesModal">Close</button>
              </div>
            </div>
          </div>
        </div>
      </Transition>
    </div>
  </div>
</template>

<script>
import { useAuthStore } from "@/stores/auth";
import apiClient from "@/services/apiClient";
import { computed, onMounted, ref } from "vue";
import { useToast } from "vue-toastification";
import BalanceHistoryTile from "@/components/BalanceHistoryTile.vue";
import { useProfileStore } from '@/stores/profile';
import { useClientStore } from '@/stores/clientStore';
import LoadingState from "@/components/LoadingState.vue";
import DashboardWalkthrough from "@/components/DashboardWalkthrough.vue";
import { useWalkthroughStore } from "@/stores/walkthrough";

export default {
  name: 'DashboardView',
  components: { BalanceHistoryTile, LoadingState, DashboardWalkthrough },
  setup() {
    const authStore = useAuthStore();
    const toast = useToast();
    const walkthroughStore = useWalkthroughStore();
    const clientStore = useClientStore();
    const tokenisedAssets = ref([]);
    const isLoading = ref(true);
    const activities = ref([]);
    const isLoadingActivities = ref(true);
    const showActivitiesModal = ref(false);
    const balanceHistoryData = ref([]);
    const isLoadingBalanceHistory = ref(true);

    const profileStore = useProfileStore();
    const user = computed(() => authStore.user);
    const accountTitle = computed(() => {
      if (!user.value || !user.value.role) return "Investor";  // Fallback role
      return user.value.role === "manager" ? "Investment Manager" : "Investor";
    });
    const pendingAssetsBalance = ref(0);
    const approvedAssetsBalance = ref(0);
    const vestedAssetsBalance = ref(0);
    const tokenisedAssetsBalance = ref(0);
    const totalAssetValue = ref(0);
    const showBreakdown = ref(false);
    const assetCategories = ref({
      pending_value: 0,
      approved_value: 0,
      tokenised_value: 0,
      vested_value: 0
    });
    const topTokenisedAssets = computed(() => {
      if (!tokenisedAssets.value || !tokenisedAssets.value.length) return [];
      return tokenisedAssets.value.slice(0, 3);
    });
    const userAvatar = computed(() =>
      profileStore.profilePicture || profileStore.defaultProfilePicture
    );

    // Show only the 5 most recent activities
    const recentActivities = computed(() => {
      return activities.value.slice(0, 5);
    });

    // Effective client count for managers
    const effectiveClientCount = computed(() => {
      if (user.value?.role === 'manager') {
        return clientStore.effectiveClientCount;
      }
      return 1; // For clients, count is always 1 (themselves)
    });

    async function fetchDashboardData() {
      try {
        if (!authStore.token) {
          throw new Error("User is not authenticated.");
        }

        // Set all loading states to true
        isLoading.value = true;
        isLoadingActivities.value = true;
        isLoadingBalanceHistory.value = true;

        // Debug logging for production
        console.log('Fetching dashboard data from:', import.meta.env.VITE_API_URL);
        console.log('Auth token present:', !!authStore.token);

        // Make a single API call to get all dashboard data
        const response = await apiClient.get("/tokenisation/dashboard-data");

        if (response.data && response.data.success) {
          // Update client count from backend response
          if (response.data.client_count !== undefined) {
            clientStore.clientCount = response.data.client_count;
          } else if (user.value?.role === 'manager') {
            // Fallback: fetch client count separately if not provided by backend
            await clientStore.fetchClientCount();
          }
          // Process assets data
          const assets = response.data.assets || [];
          tokenisedAssets.value = assets;
          const pendingAssets = assets.filter(
            (asset) => asset.approval_status === "pending"
          );
          const approvedAssets = assets.filter(
            (asset) => asset.approval_status === "approved"
          );
          pendingAssetsBalance.value = pendingAssets.reduce(
            (sum, asset) => sum + Number(asset.value || 0),
            0
          );
          approvedAssetsBalance.value = approvedAssets.reduce(
            (sum, asset) => sum + Number(asset.value || 0),
            0
          );

          // Process vested assets data
          if (response.data.vested_assets) {
            vestedAssetsBalance.value = response.data.vested_assets.reduce(
              (sum, asset) => sum + Number(asset.vested_amount || 0),
              0
            );
          }

          // Calculate tokenised assets balance separately from approved assets
          // This represents assets that have been fully tokenised
          tokenisedAssetsBalance.value = assets.filter(
            (asset) => asset.approval_status === "approved" && asset.blockchain_tx_hash
          ).reduce(
            (sum, asset) => sum + Number(asset.value || 0),
            0
          );

          // Process activities data
          activities.value = (response.data.activities || []).map(activity => ({
            ...activity,
            timestamp: activity.created_at // Ensure timestamp is set from created_at
          }));

          // Process balance history data
          balanceHistoryData.value = response.data.balance_history || [];

          // Set total asset value and categories from backend
          totalAssetValue.value = response.data.total_asset_value || 0;

          // Set asset categories
          if (response.data.asset_categories) {
            assetCategories.value = response.data.asset_categories;
          } else {
            // Fallback to calculated values if backend doesn't provide categories
            assetCategories.value = {
              pending_value: pendingAssetsBalance.value,
              approved_value: approvedAssetsBalance.value - tokenisedAssetsBalance.value,
              tokenised_value: tokenisedAssetsBalance.value,
              vested_value: vestedAssetsBalance.value
            };
          }
        } else {
          toast.error("Error loading dashboard data. Please try again.", {
            position: "top-right",
          });
        }
      } catch (error) {
        console.error("Dashboard data fetch error:", error);
        console.error("Error response:", error.response?.data);
        console.error("Error status:", error.response?.status);
        toast.error("Error loading dashboard data. Please try again.", {
          position: "top-right",
        });

        // Reset data on error
        tokenisedAssets.value = [];
        pendingAssetsBalance.value = 0;
        approvedAssetsBalance.value = 0;
        vestedAssetsBalance.value = 0;
        tokenisedAssetsBalance.value = 0;
        totalAssetValue.value = 0;
        assetCategories.value = {
          pending_value: 0,
          approved_value: 0,
          tokenised_value: 0,
          vested_value: 0
        };
        activities.value = [];
        balanceHistoryData.value = [];
      } finally {
        // Set all loading states to false
        isLoading.value = false;
        isLoadingActivities.value = false;
        isLoadingBalanceHistory.value = false;
      }
    }

    async function refreshData() {
      try {
        await fetchDashboardData();
        toast.success("Dashboard data refreshed successfully!", { position: "top-right" });
      } catch (error) {
        toast.error("Error refreshing dashboard data. Please try again.", {
          position: "top-right",
        });
      }
    }

    onMounted(async () => {
      if (!authStore.isLoggedIn) {
        this.$router.push("/login");
        return;
      }
      try {
        await authStore.fetchUserProfile();
        await fetchDashboardData();
      } catch (error) {
        toast.error("Error loading dashboard. Please try again.", { position: "top-right" });
        this.$router.push("/login");
      }
    });

    // Helper: format a number with grouping and up to `maxDecimals` decimals,
    // trimming trailing zeros and the decimal point when unnecessary.
    const formatWithMaxLen = (num, maxLen) => {
      // Try from 4 decimals down to 0 until it fits within maxLen
      for (let dec = 4; dec >= 0; dec--) {
        const s = num.toLocaleString('en-GB', {
          minimumFractionDigits: 0,
          maximumFractionDigits: dec,
          useGrouping: true
        });
        if (s.length <= maxLen) return s;
      }
      return null; // Signal that plain formatting won't fit
    };

    // Helper: compact format with K/M/B/T suffix constrained to maxLen
    const formatWithSuffix = (num, maxLen) => {
      const abs = Math.abs(num);
      const units = [
        { v: 1e12, s: 'T' },
        { v: 1e9,  s: 'B' },
        { v: 1e6,  s: 'M' },
        { v: 1e3,  s: 'K' },
      ];
      for (const { v, s } of units) {
        if (abs >= v) {
          const base = num / v;
          // Try from 4 decimals down to 0 to make it fit (no grouping with suffix)
          for (let dec = 4; dec >= 0; dec--) {
            let str = base.toLocaleString('en-GB', {
              minimumFractionDigits: 0,
              maximumFractionDigits: dec,
              useGrouping: false,
            }) + s;
            if (str.length <= maxLen) return str;
          }
        }
      }
      // As a last resort, pick the largest unit and force an integer with that suffix
      for (const { v, s } of units) {
        if (abs >= v) {
          const str = Math.round(num / v).toString() + s;
          return str.length <= maxLen ? str : str.slice(0, maxLen);
        }
      }
      // If still no unit applies (i.e., < 1,000), fallback to grouped integer trimmed to maxLen
      const grouped = Math.round(num).toLocaleString('en-GB');
      return grouped.length <= maxLen ? grouped : grouped.slice(0, maxLen);
    };

    const formatTotalBalance = () => {
      // Choose the base value: prefer backend-provided total if defined (including 0)
      const backendVal = Number(totalAssetValue.value ?? NaN);
      const hasBackend = Number.isFinite(backendVal);
      const value = hasBackend
        ? backendVal
        : (Number(pendingAssetsBalance.value || 0)
          + Number(approvedAssetsBalance.value || 0)
          + Number(vestedAssetsBalance.value || 0)
          + Number(tokenisedAssetsBalance.value || 0));

      // Ensure non-negative and finite fallback
      const safeValue = Number.isFinite(value) ? value : 0;

      // 1) Try full grouping with up to 4 decimals under 8 characters
      const MAX_LEN = 8; // total characters including commas/decimal
      const grouped = formatWithMaxLen(safeValue, MAX_LEN);
      if (grouped !== null) return grouped;

      // 2) If it doesn't fit, use compact suffix K/M/B constrained to 8 chars
      return formatWithSuffix(safeValue, MAX_LEN);
    };

    const formatCategoryValue = (value) => {
      const n = Number(value || 0);
      if (n >= 1e12) {
        return `${(n / 1e12).toFixed(1)}T`;
      } else if (n >= 1e9) {
        return `${(n / 1e9).toFixed(1)}B`;
      } else if (n >= 1e6) {
        return `${(n / 1e6).toFixed(1)}M`;
      } else if (n >= 1e3) {
        return `${(n / 1e3).toFixed(1)}K`;
      } else {
        return n.toLocaleString('en-GB');
      }
    };

    const calculatePercentage = (value) => {
      // Coerce incoming values (handles numbers and strings like "1,234.56")
      const toNum = (v) => {
        if (typeof v === 'number') return Number.isFinite(v) ? v : 0;
        if (v === null || v === undefined) return 0;
        const cleaned = String(v).replace(/,/g, '');
        const n = Number(cleaned);
        return Number.isFinite(n) ? n : 0;
      };

      const val = toNum(value);

      // Prefer backend total if positive; otherwise use the sum of category values
      const backendTotal = toNum(totalAssetValue.value);
      const summedTotal = [
        toNum(assetCategories.value?.pending_value),
        toNum(assetCategories.value?.approved_value),
        toNum(assetCategories.value?.tokenised_value),
        toNum(assetCategories.value?.vested_value)
      ].reduce((a, b) => a + b, 0);

      const total = backendTotal > 0 ? backendTotal : (summedTotal > 0 ? summedTotal : 1);

      const percentage = (val / total) * 100;
      // Cap at 100% and ensure minimum visibility of 2% if value exists
      return val > 0 ? Math.max(2, Math.min(100, percentage)) : 0;
    };

    const toggleBreakdown = () => {
      showBreakdown.value = !showBreakdown.value;
    };

    const formatActivityTitle = (activity) => {
  const titles = {
    ASSET_CREATED: `Created new asset: ${activity.metadata?.asset_name || 'Unknown Asset'}`,
    ASSET_UPDATED: `Updated asset: ${activity.metadata?.asset_name || 'Unknown Asset'}`,
    ASSET_DELETED: `Deleted asset: ${activity.metadata?.asset_name || 'Unknown Asset'}`,
    PROFILE_UPDATED: 'Updated profile information',
    LOGIN: 'Logged into account',
    LOGOUT: 'Logged out of account',
    ASSET_TOKENISED: `Tokenised asset: ${activity.metadata?.asset_name || 'Unknown Asset'}`, // ✅ Correct spelling
    ASSET_VERIFIED: `Verified asset: ${activity.metadata?.asset_name || 'Unknown Asset'}`,   // ✅ Add this
    ASSET_APPROVED: `Approved asset: ${activity.metadata?.asset_name || 'Unknown Asset'}`,
    ASSET_REJECTED: `Rejected asset: ${activity.metadata?.asset_name || 'Unknown Asset'}`
  };
  return titles[activity.type] || 'Unknown activity';
};

    const formatActivityDate = (date) => {
      if (!date) return '';
      const activityDate = new Date(date);
      const now = new Date();
      const diffInHours = (now - activityDate) / (1000 * 60 * 60);

      if (diffInHours < 24) {
        // If less than 24 hours ago, show relative time
        if (diffInHours < 1) {
          const minutes = Math.floor((now - activityDate) / (1000 * 60));
          return `${minutes} minute${minutes !== 1 ? 's' : ''} ago`;
        }
        return `${Math.floor(diffInHours)} hour${diffInHours !== 1 ? 's' : ''} ago`;
      } else if (diffInHours < 48) {
        // If yesterday
        return `Yesterday at ${activityDate.toLocaleTimeString('en-GB', {
          hour: 'numeric',
          minute: '2-digit',
          hour12: true
        })}`;
      }

      // Otherwise show full date
      return activityDate.toLocaleString('en-GB', {
        month: 'short',
        day: 'numeric',
        year: activityDate.getFullYear() !== now.getFullYear() ? 'numeric' : undefined,
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
      });
    };

    const getActivityIcon = (type) => {
      const icons = {
        ASSET_CREATED: 'bi-plus-circle',
        ASSET_UPDATED: 'bi-pencil',
        ASSET_DELETED: 'bi-trash',
        PROFILE_UPDATED: 'bi-person',
        LOGIN: 'bi-box-arrow-in-right',
        LOGOUT: 'bi-box-arrow-right',
        ASSET_TOKENISED: 'bi-coin', // British spelling
        ASSET_VERIFIED: 'bi-shield-check',
        ASSET_APPROVED: 'bi-check-circle',
        ASSET_REJECTED: 'bi-x-circle'
      };
      return icons[type] || 'bi-activity';
    };

    const getActivityColorClass = (type) => {
      const colorClasses = {
        ASSET_CREATED: 'activity-icon-success',
        ASSET_UPDATED: 'activity-icon-primary',
        ASSET_DELETED: 'activity-icon-danger',
        PROFILE_UPDATED: 'activity-icon-info',
        LOGIN: 'activity-icon-secondary',
        LOGOUT: 'activity-icon-secondary',
        ASSET_TOKENISED: 'activity-icon-warning', // British spelling
        ASSET_VERIFIED: 'activity-icon-primary',
        ASSET_APPROVED: 'activity-icon-success',
        ASSET_REJECTED: 'activity-icon-danger'
      };
      return colorClasses[type] || 'activity-icon-secondary';
    };

    const showAllActivities = () => {
      showActivitiesModal.value = true;
      // Prevent scrolling of the main content when modal is open
      document.body.style.overflow = 'hidden';
    };

    const closeActivitiesModal = () => {
      showActivitiesModal.value = false;
      // Restore scrolling when modal is closed
      document.body.style.overflow = '';
    };

    return {
      user,
      accountTitle,
      pendingAssetsBalance,
      approvedAssetsBalance,
      vestedAssetsBalance,
      tokenisedAssetsBalance,
      totalAssetValue,
      assetCategories,
      refreshData,
      topTokenisedAssets,
      isLoading,
      userAvatar,
      activities,
      recentActivities,
      isLoadingActivities,
      formatActivityDate,
      getActivityIcon,
      showActivitiesModal,
      showAllActivities,
      closeActivitiesModal,
      getActivityColorClass,
      formatActivityTitle,
      formatTotalBalance,
      formatCategoryValue,
      calculatePercentage,
      showBreakdown,
      toggleBreakdown,
      balanceHistoryData,
      isLoadingBalanceHistory,
      walkthroughStore,
      effectiveClientCount,
    };
  },
};
</script>

<style scoped lang="scss">
.dashboard-container {
  padding: 1rem 2rem;

  @media (max-width: 768px) {
    padding: 1rem;
  }
}

.dashboard-header {
  margin-bottom: 2rem;

  .header-title {
    font-size: 2rem;
    font-weight: 600;
  }

  .header-subtitle {
    font-size: 1rem;
  }

  @media (max-width: 768px) {
    text-align: center;
  }
}

.refresh-btn {
  padding: 0.75rem 1.25rem;
  font-size: 1rem;
}

.dashboard-card {
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
  }
}

.asset-balance-tile {
  .card-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--bs-body-color);
  }

  .balance-badge {
    font-size: 0.75rem;
    padding: 0.35rem 0.75rem;
    border-radius: 20px;
    font-weight: 500;

    &.badge-manager {
      background-color: var(--bs-primary-bg-subtle);
      color: var(--bs-primary);
    }

    &.badge-client {
      background-color: var(--bs-success-bg-subtle);
      color: var(--bs-success);
    }
  }

  .balance-content {
    // Total Balance Card
    .total-balance-container {
      display: flex;
      justify-content: center;
      margin-bottom: 1rem;

      .total-balance-card {
        // background: var(--glass-bg);
        border-radius: 12px;
        padding: 1.5rem 2.5rem;
        text-align: center;
        // box-shadow: var(--glass-shadow);
        min-width: 200px;
        border: 1px solid var(--glass-border);
        backdrop-filter: blur(8px);
        width: 100%;
        max-width: 400px;
        position: relative;
        overflow: hidden;

        // &::before {
        //   content: '';
        //   position: absolute;
        //   top: 0;
        //   left: 0;
        //   right: 0;
        //   height: 4px;
        //   background: var(--gradient-primary);
        // }

        .total-balance-header {
          font-size: 0.9rem;
          // color: var(--bs-secondary);
          margin-bottom: 0.5rem;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }

        .total-balance-value {
          font-size: 2.25rem;
          font-weight: 700;
          color: var(--bs-body-color);
          margin-bottom: 1rem;
        }

        .breakdown-toggle-btn {
          background: transparent;
          border: none;
          color: var(--bs-primary);
          font-size: 0.9rem;
          padding: 0.5rem 1rem;
          border-radius: 20px;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 0.5rem;
          margin: 0 auto;
          transition: all 0.2s ease;

          &:hover {
            background-color: var(--bs-primary-bg-subtle);
          }

          i {
            font-size: 0.8rem;
            transition: transform 0.2s ease;
          }
        }
      }
    }

    // Balance Breakdown
    .balance-breakdown-container {
      .breakdown-title {
        font-size: 1rem;
        font-weight: 600;
        margin-bottom: 1.25rem;
        color: var(--bs-body-color);
        position: relative;
        padding-bottom: 0.5rem;

        &:after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 0;
          width: 100%;
          height: 1px;
          background: var(--primary);
          border-radius: 3px;
        }
      }

      .balance-categories {
        display: flex;
        flex-direction: column;
        gap: 1.25rem;

        .balance-category {
display: grid;
  grid-template-columns: repeat(3, 1fr);
  text-align: left;
          gap: 0.5rem;

          .category-header {
            display: flex;
            align-items: center;
            gap: 0.75rem;

            .category-icon {
              width: 32px;
              height: 32px;
              border-radius: 8px;
              display: flex;
              align-items: center;
              justify-content: center;

              i {
                font-size: 1rem;
              }

              &.pending-icon {
                background-color: var(--bs-secondary-bg-subtle);
                color: var(--bs-secondary);
                border: 1px solid var(--bs-border-color);
              }

              &.approved-icon {
                background-color: var(--bs-primary-bg-subtle);
                color: var(--bs-primary);
                border: 1px solid rgba(var(--bs-primary-rgb), 0.2);
              }

              &.vested-icon {
                background-color: var(--bs-success-bg-subtle);
                color: var(--bs-success);
                border: 1px solid rgba(var(--bs-success-rgb), 0.2);
              }

              &.tokenised-icon {
                background-color: var(--bs-warning-bg-subtle);
                color: var(--bs-warning);
                border: 1px solid rgba(var(--bs-warning-rgb), 0.2);
              }
            }

            .category-name {
              font-size: 0.95rem;
              font-weight: 500;
              color: var(--bs-body-color);
            }
          }

          .category-value {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--bs-body-color);
            // margin-left: calc(32px + 0.75rem); // Align with category name
          }

          .category-bar-container {
            height: 8px;
            background-color: var(--bs-secondary-bg);
            border-radius: 4px;
            overflow: hidden;
            margin-top: 0.25rem;

            .category-bar {
              height: 100%;
              border-radius: 4px;
              transition: width 0.5s ease-out;

              &.pending-bar {
                background-color: var(--bs-secondary);
                box-shadow: 0 0 8px rgba(var(--bs-secondary-rgb), 0.3);
              }

              &.approved-bar {
                background-color: var(--bs-primary);
                box-shadow: 0 0 8px rgba(var(--bs-primary-rgb), 0.3);
              }

              &.vested-bar {
                background-color: var(--bs-success);
                box-shadow: 0 0 8px rgba(var(--bs-success-rgb), 0.3);
              }

              &.tokenised-bar {
                background-color: var(--bs-warning);
                box-shadow: 0 0 8px rgba(var(--bs-warning-rgb), 0.3);
              }
            }
          }
        }
      }
    }
  }

  .tile-caption {
    font-size: 0.9rem;
    margin-top: 1rem;
    color: var(--bs-secondary);
    text-align: center;
  }

  // Responsive adjustments
  @media (max-width: 768px) {
    .total-balance-container .total-balance-card {
      padding: 1.25rem 1.5rem;
      width: 100%;
    }

    .balance-category .category-value {
      margin-left: 0;
    }
  }
}

.profile-tile {
  .card-title {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: var(--bs-body-color);
  }

  .profile-info {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;

    .profile-avatar {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      object-fit: cover;
      border: 2px solid var(--bs-primary);
    }

    .profile-details {
      .profile-name {
        font-size: 1.25rem;
        font-weight: 600;
        margin: 0;
        color: var(--bs-body-color);
      }

      .profile-email {
        font-size: 0.9rem;
        color: var(--bs-secondary);
      }
    }
  }

  .membership-info {
    margin-bottom: 1rem;

    strong {
      font-size: 0.95rem;
      color: var(--bs-body-color);
    }

    span {
      font-size: 0.95rem;
      color: var(--bs-secondary);
    }
  }
}

.recent-activity-tile {
  .card-title {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: var(--bs-body-color);
  }

  .activity-list {
    .activity-item {
      padding: 1rem 0;
      border-bottom: 1px solid var(--bs-border-color);
      transition: all 0.2s ease;

      &:last-child {
        border-bottom: none;
      }

      &:hover {
        background: var(--bs-secondary-bg);
        padding-left: 0.5rem;
        padding-right: 0.5rem;
        border-radius: 8px;
      }

      .activity-icon {
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 10px;
        transition: all 0.2s ease;

        i {
          font-size: 1.2rem;
        }

        &.activity-icon-success {
          background: var(--bs-success-bg-subtle);
          color: var(--bs-success);
        }

        &.activity-icon-primary {
          background: var(--bs-primary-bg-subtle);
          color: var(--bs-primary);
        }

        &.activity-icon-danger {
          background: var(--bs-danger-bg-subtle);
          color: var(--bs-danger);
        }

        &.activity-icon-warning {
          background: var(--bs-warning-bg-subtle);
          color: var(--bs-warning);
        }

        &.activity-icon-info {
          background: var(--bs-info-bg-subtle);
          color: var(--bs-info);
        }

        &.activity-icon-secondary {
          background: var(--bs-secondary-bg-subtle);
          // color: var(--bs-secondary);
          color: var(--bs-card-border-color);
        }
      }

      .activity-content {
        .activity-title {
          font-size: 0.95rem;
          font-weight: 500;
          color: var(--bs-body-color);
          margin: 0;
        }

        .activity-details {
          font-size: 0.85rem;
          color: var(--bs-secondary);
        }

        .activity-time {
          font-size: 0.8rem;
          // color: var(--bs-secondary);
          white-space: nowrap;
          opacity:.5;
        }
      }
    }
  }
}

.tokenised-assets-tile {
  .card-title {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: var(--bs-body-color);
  }

  .asset-list {
    li {
      padding: 0.75rem 1rem;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border: none;
      background: transparent;

      &:not(:last-child) {
        border-bottom: 1px solid var(--bs-border-color);
      }

      .asset-info {
        text-align: left;
        .asset-name {
          color: var(--bs-body-color);
        }
        small {
          color: var(--bs-secondary);
        }
      }

      .asset-value {
        color: var(--bs-success);
      }
    }
  }
}

// Modal styles
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  z-index: 1040;
}

.modal-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  z-index: 1050;
  overflow-y: auto;

  .modal-dialog {
    margin: 0;
    width: 100%;
    max-width: 800px;
    pointer-events: auto;
  }

  .modal-content {
    background: var(--bs-body-bg);
    border: 1px solid var(--bs-border-color);
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
  }

  .modal-header {
    border-bottom: 1px solid var(--bs-border-color);
    padding: 1rem 1.5rem;

    .modal-title {
      font-size: 1.25rem;
      font-weight: 600;
      margin: 0;
      display: flex;
      align-items: center;
      color: var(--bs-body-color);
    }

    .btn-close {
      background: none;
      border: none;
      color: var(--bs-body-color);
      opacity: 0.7;
      transition: opacity 0.2s;

      &:hover {
        opacity: 1;
      }
    }
  }

  .modal-body {
    padding: 1.5rem;
    max-height: calc(100vh - 200px);
    overflow-y: auto;

    &::-webkit-scrollbar {
      width: 8px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    &::-webkit-scrollbar-thumb {
      background-color: var(--bs-border-color);
      border-radius: 4px;

      &:hover {
        background-color: var(--bs-secondary);
      }
    }

    .activity-list {
      .activity-item {
        padding: 1rem;
        border-radius: 8px;
        transition: background-color 0.2s;

        &:hover {
          background-color: var(--bs-secondary-bg);
        }

        &:not(:last-child) {
          margin-bottom: 0.5rem;
        }
      }
    }
  }

  .modal-footer {
    border-top: 1px solid var(--bs-border-color);
    padding: 1rem 1.5rem;
  }
}

// Modal transitions
.modal-inner-enter-active,
.modal-inner-leave-active {
  transition: all 0.3s ease;
}

.modal-inner-enter-from,
.modal-inner-leave-to {
  opacity: 0;
  transform: scale(0.95);
}

.backdrop-enter-active,
.backdrop-leave-active {
  transition: opacity 0.3s ease;
}

.backdrop-enter-from,
.backdrop-leave-to {
  opacity: 0;
}

// Slide fade transitions for breakdown
.slide-fade-enter-active {
  transition: all 0.3s ease-out;
}

.slide-fade-leave-active {
  transition: all 0.2s cubic-bezier(1, 0.5, 0.8, 1);
}

.slide-fade-enter-from,
.slide-fade-leave-to {
  transform: translateY(-10px);
  opacity: 0;
}
.balance-content {
  width: 100%;
}
</style>


// Large spinner for loading state in activity tile
.spinner-lg {
  width: 3rem;
  height: 3rem;
  border-width: 0.4em;
}
