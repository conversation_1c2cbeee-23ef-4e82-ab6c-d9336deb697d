// Import required modules
import express from "express";
import { ethers } from "ethers";
import { v4 as uuidv4 } from "uuid";
import pg from "pg";
import dotenv from "dotenv";
import { authenticateToken } from '../middlewares/auth.js';
import { Asset } from '../setup/models.js';
import { ActivityService, ACTIVITY_TYPES } from '../services/activityService.js';

dotenv.config();

const router = express.Router();
const { Pool } = pg;
const pool = new Pool({ connectionString: process.env.DATABASE_URL });

// **🛠️ Blockchain Signing Function**
async function signAsset(assetId, assetData) {
  try {
    // Ethereum Testnet Configuration (Goerli or Sepolia)
    const provider = new ethers.JsonRpcProvider(process.env.ETH_RPC_URL);
    const wallet = new ethers.Wallet(process.env.WALLET_PRIVATE_KEY, provider);

    // Generate Message for Signature
    const message = JSON.stringify({ assetId, assetData });
    const signature = await wallet.signMessage(message);
    return signature;
  } catch (error) {
    console.error("Blockchain signing failed:", error);
    return null;
  }
}

// Helper function: Update a user's balance history snapshot
async function updateBalanceHistoryForUser(userId) {
  try {
    // Query all assets owned by the user, joining approval status from asset_approvals.
    const query = `
      SELECT ta.value, aa.status
      FROM tokenised_assets ta
      LEFT JOIN ownership_records orc ON ta.id = orc.asset_id
      LEFT JOIN asset_approvals aa ON ta.id = aa.asset_id
      WHERE orc.owner_id = $1
    `;
    const result = await pool.query(query, [userId]);

    let pending = 0;
    let approved = 0;

    result.rows.forEach(row => {
      const val = parseFloat(row.value) || 0;
      if (row.status === 'approved') {
        approved += val;
      } else {
        pending += val;
      }
    });
    const total = pending + approved;
    await pool.query(
      `INSERT INTO balance_history (user_id, asset_type, pending_balance, approved_balance, total_balance)
       VALUES ($1, 'all', $2, $3, $4)`,
      [userId, pending, approved, total]
    );
  } catch (error) {
    console.error("Error updating balance history for user", userId, error);
  }
}

// === Modified Tokenisation Endpoint ===
router.post("/tokenise", authenticateToken, async (req, res) => {
  let { name, type, value, ownership_percentage, supporting_documents, client_id, metadata = {} } = req.body;
  if (!name || !type || !value || !ownership_percentage || !client_id) {
    return res.status(400).json({ error: "All fields are required, including the client." });
  }
  try {
    const assetId = uuidv4();
    const managerId = req.user.id;
    if (client_id === "self") {
      client_id = req.user.id;
    }
    if (isNaN(parseInt(client_id))) {
      return res.status(400).json({ error: "Invalid client ID." });
    }
    const blockchainSignature = await signAsset(assetId, { name, type, value, ownership_percentage, metadata });
    if (!blockchainSignature) {
      return res.status(500).json({ error: "Blockchain signing failed." });
    }
    const assetResult = await pool.query(
      `INSERT INTO tokenised_assets (name, type, value, metadata, supporting_documents, user_id, blockchain_tx_hash)
       VALUES ($1, $2, $3, $4, $5, $6, $7) RETURNING *`,
      [
        name,
        type,
        value,
        metadata,
        (supporting_documents && supporting_documents !== "")
          ? supporting_documents.split(",").map(doc => doc.trim())
          : [],
        managerId,
        blockchainSignature
      ]
    );
    const asset = assetResult.rows[0];
    const ownershipSignature = await signAsset(assetId, { ownership_percentage });
    if (!ownershipSignature) {
      return res.status(500).json({ error: "Blockchain signing for ownership record failed." });
    }
    const ownershipResult = await pool.query(
      `INSERT INTO ownership_records (asset_id, owner_id, ownership_percentage, blockchain_tx_hash)
       VALUES ($1, $2, $3, $4) RETURNING *`,
      [asset.id, client_id, ownership_percentage, ownershipSignature]
    );

    // Create a pending approval record.
    await pool.query(
      `INSERT INTO asset_approvals (asset_id, status) VALUES ($1, 'pending')`,
      [asset.id]
    );

    // Notify client.
    await pool.query(
      `INSERT INTO notifications (user_id, type, message, status)
       VALUES ($1, $2, $3, 'unread')`,
      [client_id, 'asset_created', `A new tokenised asset has been created for you: ${name}`]
    );

    // Update balance history for the client.
    updateBalanceHistoryForUser(client_id);

    // Log asset creation activity
    await ActivityService.logActivity(
      req.user.id,
      ACTIVITY_TYPES.ASSET_CREATED,
      `Created new asset: ${name}`,
      { assetId: asset.id, assetType: asset.type }
    );

    res.status(201).json({
      message: "Asset tokenised successfully.",
      asset,
      ownership_record: ownershipResult.rows[0],
      blockchainSignature
    });
  } catch (error) {
    console.error("❌ Error tokenising asset:", error);
    res.status(500).json({ error: "Failed to tokenise asset." });
  }
});

// **📌 2. Retrieve Tokenised Assets (List All)**
router.get("/", authenticateToken, async (req, res) => {
  try {
    let query;
    let params;

    if (req.user.role === "manager") {
      // For managers: show assets where the asset creator's manager_id equals the current manager's ID.
      query = `
SELECT
            ta.id AS asset_id,
            ta.name,
            ta.type,
            ta.value,
            ta.metadata,
            ta.supporting_documents,
            ta.blockchain_tx_hash AS asset_blockchain_tx,
            ta.created_at AS asset_created_at,
            orc.id AS ownership_id,
            orc.owner_id,
            orc.ownership_percentage,
            orc.blockchain_tx_hash AS ownership_blockchain_tx,
            orc.created_at AS ownership_created_at,
            COALESCE(aa.status, 'pending') AS approval_status,
            aa.blockchain_tx_hash AS approval_blockchain_tx,
            aa.created_at AS approval_created_at,
            ii.company_name AS owner_company_name,
            ii.contact_name AS owner_name,
            ii.id AS owner_institutional_id
          FROM tokenised_assets ta
          LEFT JOIN ownership_records orc ON ta.id = orc.asset_id
          LEFT JOIN asset_approvals aa ON ta.id = aa.asset_id
          LEFT JOIN institutional_investors ii ON orc.owner_id = ii.id
          WHERE ta.user_id IN (
            SELECT id FROM institutional_investors WHERE manager_id = $1
          )
          ORDER BY ta.created_at DESC
        `;
      params = [req.user.id];
    } else {
      // For clients: show assets where the client is the owner.
      query = `
          SELECT
            ta.id AS asset_id,
            ta.name,
            ta.type,
            ta.value,
            ta.metadata,
            ta.supporting_documents,
            ta.blockchain_tx_hash AS asset_blockchain_tx,
            ta.created_at AS asset_created_at,
            orc.id AS ownership_id,
            orc.owner_id,
            orc.ownership_percentage,
            orc.blockchain_tx_hash AS ownership_blockchain_tx,
            orc.created_at AS ownership_created_at,
            COALESCE(aa.status, 'pending') AS approval_status,
            aa.blockchain_tx_hash AS approval_blockchain_tx,
            aa.created_at AS approval_created_at,
            ii.company_name AS owner_name,
            ii.id AS owner_institutional_id
          FROM tokenised_assets ta
          LEFT JOIN ownership_records orc ON ta.id = orc.asset_id
          LEFT JOIN asset_approvals aa ON ta.id = aa.asset_id
          LEFT JOIN institutional_investors ii ON orc.owner_id = ii.id
          WHERE orc.owner_id = $1
          ORDER BY ta.created_at DESC
        `;
      params = [req.user.id];
    }

    const result = await pool.query(query, params);

    if (result.rows.length === 0) {
      return res.status(204).json({ message: "No tokenised assets found." });
    }

    // Debug logging
    console.log("Raw query results:", result.rows);

    // Group data by asset_id, aggregating ownership records
    const assets = {};
    result.rows.forEach(row => {
      if (!assets[row.asset_id]) {
        assets[row.asset_id] = {
          id: row.asset_id,
          name: row.name,
          type: row.type,
          value: row.value,
          metadata: row.metadata || {},
          supporting_documents: row.supporting_documents || [],
          blockchain_tx_hash: row.asset_blockchain_tx,
          created_at: row.asset_created_at,
          approval_status: row.approval_status,
          approval_blockchain_tx: row.approval_blockchain_tx,
          approval_created_at: row.approval_created_at,
          ownership_records: []
        };
      }
      if (row.ownership_id) {
        // Debug logging for ownership record
        console.log("Processing ownership record:", {
          asset_id: row.asset_id,
          ownership_id: row.ownership_id,
          owner_id: row.owner_id,
          owner_name: row.owner_name,
          owner_institutional_id: row.owner_institutional_id
        });

        assets[row.asset_id].ownership_records.push({
          ownership_id: row.ownership_id,
          owner_id: row.owner_id,
          owner_name: row.owner_name || 'Unknown',
          ownership_percentage: row.ownership_percentage,
          blockchain_tx_hash: row.ownership_blockchain_tx,
          created_at: row.ownership_created_at
        });
      }
    });

    // Debug logging for final assets object
    console.log("Processed assets:", assets);

    res.status(200).json({ assets: Object.values(assets) });
  } catch (error) {
    console.error("Error fetching tokenised assets:", error);
    // Log the specific error details
    console.error("Error details:", {
      message: error.message,
      stack: error.stack,
      query: query,
      params: params
    });
    res.status(500).json({
      error: "Failed to fetch tokenised assets.",
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});


// **📌 5. Verify a Tokenised Asset**
// (This endpoint updates the asset's metadata with verification info and notifies the client.)
router.patch("/verify/:assetId", authenticateToken, async (req, res) => {
  if (req.user.role !== "manager") {
    return res.status(403).json({ error: "Only managers can verify assets." });
  }

  try {
    const { assetId } = req.params;
    const managerId = req.user.id;

    const updateQuery = `
        UPDATE tokenised_assets
        SET metadata = jsonb_set(COALESCE(metadata, '{}'), '{verification}', to_jsonb($1::text))
        WHERE id = $2 RETURNING *
      `;
    const verificationInfo = `verified_by: ${managerId}`;
    const result = await pool.query(updateQuery, [verificationInfo, assetId]);

    if (result.rowCount === 0) {
      return res.status(404).json({ error: "Asset not found." });
    }

    const asset = result.rows[0];

    // Retrieve the client (owner) from ownership_records.
    const ownershipRes = await pool.query(
      `SELECT owner_id FROM ownership_records WHERE asset_id = $1 LIMIT 1`,
      [assetId]
    );
    let client_id = ownershipRes.rows.length > 0 ? ownershipRes.rows[0].owner_id : null;

    // Notify the client.
    if (client_id) {
      await pool.query(
        `INSERT INTO notifications (user_id, type, message, status)
           VALUES ($1, $2, $3, 'unread')`,
        [client_id, 'asset_verified', `Your asset ${asset.name} has been verified.`]
      );
    }

    // Log asset verification activity
    await ActivityService.logActivity(
      req.user.id,
      ACTIVITY_TYPES.ASSET_VERIFIED,
      `Verified asset: ${asset.name}`,
      { assetId: asset.id }
    );

    res.json({ message: "Asset verified successfully.", asset });
  } catch (error) {
    console.error("❌ Error verifying asset:", error);
    res.status(500).json({ error: "Failed to verify asset." });
  }
});

// **📌 6. Approve a Tokenised Asset**
// This endpoint updates the asset_approvals record to "approved" and issues a blockchain signature for the approval event.
router.patch("/approve/:assetId", authenticateToken, async (req, res) => {
  if (req.user.role !== "manager") {
    return res.status(403).json({ error: "Only managers can approve assets." });
  }

  try {
    const { assetId } = req.params;
    const managerId = req.user.id;
    const approvalSignature = await signAsset(assetId, { approved_by: managerId, timestamp: new Date().toISOString() });
    if (!approvalSignature) {
      return res.status(500).json({ error: "Blockchain signing for approval failed." });
    }

    const approvalResult = await pool.query(
      `UPDATE asset_approvals
         SET status = 'approved', blockchain_tx_hash = $1, created_at = NOW()
         WHERE asset_id = $2 RETURNING *`,
      [approvalSignature, assetId]
    );

    if (approvalResult.rowCount === 0) {
      return res.status(404).json({ error: "Approval record not found for asset." });
    }

    // Optionally, notify the client.
    const ownershipRes = await pool.query(
      `SELECT owner_id FROM ownership_records WHERE asset_id = $1 LIMIT 1`,
      [assetId]
    );
    if (ownershipRes.rows.length > 0) {
      const client_id = ownershipRes.rows[0].owner_id;
      await pool.query(
        `INSERT INTO notifications (user_id, type, message, status)
           VALUES ($1, $2, $3, 'unread')`,
        [client_id, 'asset_approved', `Your asset has been approved by your manager.`]
      );
    }

    // Log approval activity
    await ActivityService.logActivity(
      req.user.id,
      ACTIVITY_TYPES.ASSET_APPROVED,
      `Approved asset: ${approvalResult.rows[0].asset_id}`,
      { assetId: approvalResult.rows[0].asset_id }
    );

    // Update balance history for the client.
    updateBalanceHistoryForUser(client_id);

    res.json({ message: "Asset approved successfully.", approval: approvalResult.rows[0] });
  } catch (error) {
    console.error("❌ Error approving asset:", error);
    res.status(500).json({ error: "Failed to approve asset." });
  }
});

// === Modified Approval Endpoint (PATCH /approve/:assetId) ===
router.patch("/approve/:assetId", authenticateToken, async (req, res) => {
  if (req.user.role !== "manager") {
    return res.status(403).json({ error: "Only managers can approve assets." });
  }
  try {
    const { assetId } = req.params;
    const managerId = req.user.id;
    const approvalSignature = await signAsset(assetId, { approved_by: managerId, timestamp: new Date().toISOString() });
    if (!approvalSignature) {
      return res.status(500).json({ error: "Blockchain signing for approval failed." });
    }
    const approvalResult = await pool.query(
      `UPDATE asset_approvals
       SET status = 'approved', blockchain_tx_hash = $1, created_at = NOW()
       WHERE asset_id = $2 RETURNING *`,
      [approvalSignature, assetId]
    );
    if (approvalResult.rowCount === 0) {
      return res.status(404).json({ error: "Approval record not found for asset." });
    }
    // Notify client.
    const ownershipRes = await pool.query(
      `SELECT owner_id FROM ownership_records WHERE asset_id = $1 LIMIT 1`,
      [assetId]
    );
    if (ownershipRes.rows.length > 0) {
      const client_id = ownershipRes.rows[0].owner_id;
      await pool.query(
        `INSERT INTO notifications (user_id, type, message, status)
         VALUES ($1, $2, $3, 'unread')`,
        [client_id, 'asset_approved', `Your asset has been approved by your manager.`]
      );
      // Update balance history for the client.
      updateBalanceHistoryForUser(client_id);
    }

    // Log approval activity
    await ActivityService.logActivity(
      req.user.id,
      ACTIVITY_TYPES.ASSET_APPROVED,
      `Approved asset: ${approvalResult.rows[0].asset_id}`,
      { assetId: approvalResult.rows[0].asset_id }
    );

    res.json({ message: "Asset approved successfully.", approval: approvalResult.rows[0] });
  } catch (error) {
    console.error("❌ Error approving asset:", error);
    res.status(500).json({ error: "Failed to approve asset." });
  }
});

// === Balance History Endpoints ===

// Helper function: Get balance history for a specific user
async function getUserBalanceHistory(userId) {
  try {
    const result = await pool.query(
      `SELECT * FROM balance_history
       WHERE user_id = $1
       ORDER BY recorded_at ASC`,
      [userId]
    );
    return result.rows;
  } catch (error) {
    console.error("Error fetching user balance history:", error);
    throw error;
  }
}

// Helper function: Get aggregated balance history for all clients of a manager
async function getManagerBalanceHistory(managerId) {
  try {
    // First get all clients managed by this manager
    const clientsResult = await pool.query(
      `SELECT id FROM institutional_investors
       WHERE manager_id = $1`,
      [managerId]
    );

    const clientIds = clientsResult.rows.map(r => r.id);

    if (!clientIds.length) {
      return [];
    }

    // Get aggregated balance history for all clients
    const historyResult = await pool.query(
      `SELECT
         recorded_at,
         asset_type,
         SUM(pending_balance) as pending_balance,
         SUM(approved_balance) as approved_balance,
         SUM(total_balance) as total_balance
       FROM balance_history
       WHERE user_id = ANY($1)
       GROUP BY recorded_at, asset_type
       ORDER BY recorded_at ASC, asset_type`,
      [clientIds]
    );

    return historyResult.rows;
  } catch (error) {
    console.error("Error fetching manager's clients balance history:", error);
    throw error;
  }
}

// Endpoint for retrieving balance history
router.get("/balance-history/user/:userId", authenticateToken, async (req, res) => {
  try {
    const { userId } = req.params;

    // Verify access permissions
    if (parseInt(userId) !== req.user.id && req.user.role !== "manager") {
      return res.status(403).json({
        success: false,
        message: "You don't have permission to view this balance history"
      });
    }

    const balanceHistory = await getUserBalanceHistory(userId);

    res.json({
      success: true,
      balance_history: balanceHistory
    });
  } catch (error) {
    console.error("Error in balance history endpoint:", error);
    res.status(500).json({
      success: false,
      message: "Failed to fetch balance history"
    });
  }
});

// Endpoint for manager to get aggregated balance history
router.get("/balance-history/manager", authenticateToken, async (req, res) => {
  try {
    // Verify manager role
    if (req.user.role !== "manager") {
      return res.status(403).json({
        success: false,
        message: "Only managers can access this endpoint"
      });
    }

    const balanceHistory = await getManagerBalanceHistory(req.user.id);

    res.json({
      success: true,
      balance_history: balanceHistory
    });
  } catch (error) {
    console.error("Error in manager balance history endpoint:", error);
    res.status(500).json({
      success: false,
      message: "Failed to fetch manager balance history"
    });
  }
});

// Import vesting service
import vestingService from '../services/vestingService.js';

// Consolidated Dashboard Data Endpoint
router.get("/dashboard-data", authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const userRole = req.user.role;

    // Prepare response object
    const dashboardData = {
      success: true,
      assets: [],
      activities: [],
      balance_history: [],
      vested_assets: [],
      client_count: 0
    };

    // 1. Fetch assets data
    try {
      let query;
      let params;

      if (userRole === "manager") {
        query = `
          SELECT
            ta.id AS asset_id,
            ta.name,
            ta.type,
            ta.value,
            ta.metadata,
            ta.supporting_documents,
            ta.blockchain_tx_hash AS asset_blockchain_tx,
            ta.created_at AS asset_created_at,
            orc.id AS ownership_id,
            orc.owner_id,
            orc.ownership_percentage,
            orc.blockchain_tx_hash AS ownership_blockchain_tx,
            orc.created_at AS ownership_created_at,
            COALESCE(aa.status, 'pending') AS approval_status,
            aa.blockchain_tx_hash AS approval_blockchain_tx,
            aa.created_at AS approval_created_at,
            ii.company_name AS owner_company_name,
            ii.contact_name AS owner_name,
            ii.id AS owner_institutional_id
          FROM tokenised_assets ta
          LEFT JOIN ownership_records orc ON ta.id = orc.asset_id
          LEFT JOIN asset_approvals aa ON ta.id = aa.asset_id
          LEFT JOIN institutional_investors ii ON orc.owner_id = ii.id
          WHERE ta.user_id IN (
            SELECT id FROM institutional_investors WHERE manager_id = $1
          )
          ORDER BY ta.created_at DESC
        `;
        params = [userId];
      } else {
        query = `
          SELECT
            ta.id AS asset_id,
            ta.name,
            ta.type,
            ta.value,
            ta.metadata,
            ta.supporting_documents,
            ta.blockchain_tx_hash AS asset_blockchain_tx,
            ta.created_at AS asset_created_at,
            orc.id AS ownership_id,
            orc.owner_id,
            orc.ownership_percentage,
            orc.blockchain_tx_hash AS ownership_blockchain_tx,
            orc.created_at AS ownership_created_at,
            COALESCE(aa.status, 'pending') AS approval_status,
            aa.blockchain_tx_hash AS approval_blockchain_tx,
            aa.created_at AS approval_created_at,
            ii.company_name AS owner_name,
            ii.id AS owner_institutional_id
          FROM tokenised_assets ta
          LEFT JOIN ownership_records orc ON ta.id = orc.asset_id
          LEFT JOIN asset_approvals aa ON ta.id = aa.asset_id
          LEFT JOIN institutional_investors ii ON orc.owner_id = ii.id
          WHERE orc.owner_id = $1
          ORDER BY ta.created_at DESC
        `;
        params = [userId];
      }

      const result = await pool.query(query, params);

      if (result.rows.length > 0) {
        // Group data by asset_id, aggregating ownership records
        const assets = {};
        result.rows.forEach(row => {
          if (!assets[row.asset_id]) {
            assets[row.asset_id] = {
              id: row.asset_id,
              name: row.name,
              type: row.type,
              value: row.value,
              metadata: row.metadata || {},
              supporting_documents: row.supporting_documents || [],
              blockchain_tx_hash: row.asset_blockchain_tx,
              created_at: row.asset_created_at,
              approval_status: row.approval_status,
              approval_blockchain_tx: row.approval_blockchain_tx,
              approval_created_at: row.approval_created_at,
              ownership_records: []
            };
          }
          if (row.ownership_id) {
            assets[row.asset_id].ownership_records.push({
              ownership_id: row.ownership_id,
              owner_id: row.owner_id,
              owner_name: row.owner_name || 'Unknown',
              ownership_percentage: row.ownership_percentage,
              blockchain_tx_hash: row.ownership_blockchain_tx,
              created_at: row.ownership_created_at
            });
          }
        });

        dashboardData.assets = Object.values(assets);
      }
    } catch (error) {
      console.error("Error fetching assets for dashboard:", error);
    }

    // 2. Fetch activities data
    try {
      // Query the notifications table instead of activities (which doesn't exist)
      // The Activity model is mapped to the notifications table in models.js
      const activityQuery = `
        SELECT * FROM notifications
        WHERE user_id = $1
        ORDER BY created_at DESC
        LIMIT 10
      `;

      const activityResult = await pool.query(activityQuery, [userId]);
      dashboardData.activities = activityResult.rows;
    } catch (error) {
      console.error("Error fetching activities for dashboard:", error);
      // Add more detailed error logging
      console.error("Activity query error details:", {
        message: error.message,
        stack: error.stack
      });
    }

    // 3. Fetch balance history data
    try {
      if (userRole === "manager") {
        dashboardData.balance_history = await getManagerBalanceHistory(userId);
      } else {
        dashboardData.balance_history = await getUserBalanceHistory(userId);
      }
    } catch (error) {
      console.error("Error fetching balance history for dashboard:", error);
      // Add more detailed error logging
      console.error("Balance history error details:", {
        message: error.message,
        stack: error.stack,
        userRole: userRole,
        userId: userId
      });
    }

    // 4. Fetch vested assets data
    try {
      let vestedAssets;
      if (userRole === "manager") {
        vestedAssets = await vestingService.getManagerVestingRecords(userId);
      } else {
        vestedAssets = await vestingService.getUserVestingRecords(userId);
      }

      // Process vested assets to include vested amount calculation
      dashboardData.vested_assets = vestedAssets.map(asset => {
        // Calculate vesting progress based on schedule
        const schedule = asset.vesting_schedule;
        const startDate = new Date(schedule.start_date);
        const totalDays = schedule.total_days || 0;
        const now = new Date();

        // Calculate elapsed days since vesting started
        const elapsedDays = Math.max(0, Math.floor((now - startDate) / (1000 * 60 * 60 * 24)));

        // Calculate vested percentage based on elapsed days
        const vestedPercentage = Math.min(100, (elapsedDays / totalDays) * 100);

        // Calculate vested amount based on asset value and vested percentage
        const vestedAmount = (asset.asset_value * vestedPercentage) / 100;

        return {
          ...asset,
          vested_amount: vestedAmount,
          vesting_progress: vestedPercentage
        };
      });
    } catch (error) {
      console.error("Error fetching vested assets for dashboard:", error);
      console.error("Vested assets error details:", {
        message: error.message,
        stack: error.stack,
        userRole: userRole,
        userId: userId
      });
    }

    // 5. Fetch total asset value and category breakdowns (similar to reports view)
    try {
      let totalValueQuery;
      if (userRole === "manager") {
        totalValueQuery = `
          WITH user_assets AS (
            SELECT DISTINCT ta.id
            FROM tokenised_assets ta
            LEFT JOIN ownership_records ownership ON ta.id = ownership.asset_id
            WHERE 1=1
          )
          SELECT
            COALESCE(SUM(ta.value), 0) as total_value,
            COALESCE(SUM(CASE WHEN COALESCE(aa.status, 'pending') = 'pending' THEN ta.value ELSE 0 END), 0) as pending_value,
            COALESCE(SUM(CASE WHEN COALESCE(aa.status, 'pending') = 'approved' THEN ta.value ELSE 0 END), 0) as approved_value,
            COALESCE(SUM(CASE WHEN COALESCE(aa.status, 'pending') = 'approved' AND ta.blockchain_tx_hash IS NOT NULL THEN ta.value ELSE 0 END), 0) as tokenised_value
          FROM tokenised_assets ta
          INNER JOIN user_assets ua ON ta.id = ua.id
          LEFT JOIN asset_approvals aa ON ta.id = aa.asset_id
          LEFT JOIN ownership_records ownership ON ta.id = ownership.asset_id
        `;
      } else {
        totalValueQuery = `
          WITH user_assets AS (
            SELECT DISTINCT ta.id
            FROM tokenised_assets ta
            LEFT JOIN ownership_records ownership ON ta.id = ownership.asset_id
            WHERE ownership.owner_id = $1
          )
          SELECT
            COALESCE(SUM(ta.value), 0) as total_value,
            COALESCE(SUM(CASE WHEN COALESCE(aa.status, 'pending') = 'pending' THEN ta.value ELSE 0 END), 0) as pending_value,
            COALESCE(SUM(CASE WHEN COALESCE(aa.status, 'pending') = 'approved' THEN ta.value ELSE 0 END), 0) as approved_value,
            COALESCE(SUM(CASE WHEN COALESCE(aa.status, 'pending') = 'approved' AND ta.blockchain_tx_hash IS NOT NULL THEN ta.value ELSE 0 END), 0) as tokenised_value
          FROM tokenised_assets ta
          INNER JOIN user_assets ua ON ta.id = ua.id
          LEFT JOIN asset_approvals aa ON ta.id = aa.asset_id
          LEFT JOIN ownership_records ownership ON ta.id = ownership.asset_id
        `;
      }

      const totalValueResult = await pool.query(totalValueQuery, userRole === "manager" ? [] : [userId]);
      const valueData = totalValueResult.rows[0] || { total_value: 0, pending_value: 0, approved_value: 0, tokenised_value: 0 };

      // Calculate vested value from vested assets
      const vestedValue = dashboardData.vested_assets.reduce(
        (sum, asset) => sum + Number(asset.vested_amount || 0),
        0
      );

      // Store all values in the response
      dashboardData.total_asset_value = parseFloat(valueData.total_value || 0);
      dashboardData.asset_categories = {
        pending_value: parseFloat(valueData.pending_value || 0),
        approved_value: parseFloat(valueData.approved_value || 0) - parseFloat(valueData.tokenised_value || 0), // Approved but not tokenised
        tokenised_value: parseFloat(valueData.tokenised_value || 0),
        vested_value: vestedValue
      };
    } catch (error) {
      console.error("Error fetching total asset value for dashboard:", error);
      console.error("Total asset value error details:", {
        message: error.message,
        stack: error.stack,
        userRole: userRole,
        userId: userId
      });
      dashboardData.total_asset_value = 0;
      dashboardData.asset_categories = {
        pending_value: 0,
        approved_value: 0,
        tokenised_value: 0,
        vested_value: 0
      };
    }

    // 6. Fetch client count for managers
    try {
      if (userRole === "manager") {
        const clientCountResult = await pool.query(
          `SELECT COUNT(*) as count
           FROM institutional_investors
           WHERE manager_id = $1`,
          [userId]
        );
        dashboardData.client_count = parseInt(clientCountResult.rows[0]?.count || 0);
      } else {
        dashboardData.client_count = 1; // For clients, count is always 1 (themselves)
      }
    } catch (error) {
      console.error("Error fetching client count for dashboard:", error);
      dashboardData.client_count = 0;
    }

    // Return all data, even if some parts failed
    res.json(dashboardData);
  } catch (error) {
    console.error("Error fetching dashboard data:", error);
    // Add more detailed error logging
    console.error("Dashboard data error details:", {
      message: error.message,
      stack: error.stack
    });
    res.status(500).json({
      success: false,
      message: "Failed to fetch dashboard data",
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// Create new asset
router.post('/', authenticateToken, async (req, res) => {
  try {
    const asset = await Asset.create({
      ...req.body,
      userId: req.user.id
    });

    // Log asset creation activity
    await ActivityService.logActivity(
      req.user.id,
      ACTIVITY_TYPES.ASSET_CREATED,
      `Created new asset: ${asset.name}`,
      { assetId: asset.id, assetType: asset.type }
    );

    res.json({ success: true, asset });
  } catch (error) {
    console.error('Error creating asset:', error);
    res.status(500).json({ success: false, message: 'Error creating asset' });
  }
});

// Update asset
router.put('/:id', authenticateToken, async (req, res) => {
  try {
    const asset = await Asset.findByPk(req.params.id);

    if (!asset) {
      return res.status(404).json({ success: false, message: 'Asset not found' });
    }

    if (asset.userId !== req.user.id && req.user.role !== 'manager') {
      return res.status(403).json({ success: false, message: 'Unauthorized' });
    }

    await asset.update(req.body);

    // Log asset update activity
    await ActivityService.logActivity(
      req.user.id,
      ACTIVITY_TYPES.ASSET_UPDATED,
      `Updated asset: ${asset.name}`,
      { assetId: asset.id, changes: req.body }
    );

    res.json({ success: true, asset });
  } catch (error) {
    console.error('Error updating asset:', error);
    res.status(500).json({ success: false, message: 'Error updating asset' });
  }
});

// Delete asset
router.delete('/:id', authenticateToken, async (req, res) => {
  try {
    const asset = await Asset.findByPk(req.params.id);

    if (!asset) {
      return res.status(404).json({ success: false, message: 'Asset not found' });
    }

    if (asset.userId !== req.user.id && req.user.role !== 'manager') {
      return res.status(403).json({ success: false, message: 'Unauthorized' });
    }

    const assetName = asset.name;
    await asset.destroy();

    // Log asset deletion activity
    await ActivityService.logActivity(
      req.user.id,
      ACTIVITY_TYPES.ASSET_DELETED,
      `Deleted asset: ${assetName}`,
      { assetId: req.params.id }
    );

    res.json({ success: true, message: 'Asset deleted successfully' });
  } catch (error) {
    console.error('Error deleting asset:', error);
    res.status(500).json({ success: false, message: 'Error deleting asset' });
  }
});

// Approve/Reject asset
router.post('/:id/approval', authenticateToken, async (req, res) => {
  try {
    if (req.user.role !== 'manager') {
      return res.status(403).json({ success: false, message: 'Unauthorized' });
    }

    const asset = await Asset.findByPk(req.params.id);
    if (!asset) {
      return res.status(404).json({ success: false, message: 'Asset not found' });
    }

    const { status, reason } = req.body;
    await asset.update({
      approval_status: status,
      approval_reason: reason || null
    });

    // Log approval activity
    await ActivityService.logActivity(
      req.user.id,
      status === 'approved' ? ACTIVITY_TYPES.ASSET_APPROVED : ACTIVITY_TYPES.ASSET_REJECTED,
      `${status === 'approved' ? 'Approved' : 'Rejected'} asset: ${asset.name}`,
      {
        assetId: asset.id,
        reason: reason || null
      }
    );

    res.json({ success: true, asset });
  } catch (error) {
    console.error('Error updating asset approval:', error);
    res.status(500).json({ success: false, message: 'Error updating asset approval' });
  }
});

// **📌 3. Fetch a Single Tokenised Asset**
router.get("/:id", authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;
    const userRole = req.user.role;

    let query;
    let values;

    if (userRole === "manager") {
      // Manager: asset must have been created by them.
      query = `
          SELECT
            ta.*,
            orc.id AS ownership_id,
            orc.owner_id,
            orc.ownership_percentage,
            orc.blockchain_tx_hash AS ownership_blockchain_tx,
            COALESCE(aa.status, 'pending') AS approval_status,
            aa.blockchain_tx_hash AS approval_blockchain_tx
          FROM tokenised_assets ta
          LEFT JOIN ownership_records orc ON ta.id = orc.asset_id
          LEFT JOIN asset_approvals aa ON ta.id = aa.asset_id
          WHERE ta.id = $1 AND ta.user_id = $2
        `;
      values = [id, userId];
    } else {
      // Client: asset must be owned by them.
      query = `
          SELECT
            ta.*,
            orc.id AS ownership_id,
            orc.owner_id,
            orc.ownership_percentage,
            orc.blockchain_tx_hash AS ownership_blockchain_tx,
            COALESCE(aa.status, 'pending') AS approval_status,
            aa.blockchain_tx_hash AS approval_blockchain_tx
          FROM tokenised_assets ta
          LEFT JOIN ownership_records orc ON ta.id = orc.asset_id
          LEFT JOIN asset_approvals aa ON ta.id = aa.asset_id
          WHERE ta.id = $1 AND orc.owner_id = $2
        `;
      values = [id, userId];
    }

    const result = await pool.query(query, values);
    if (result.rows.length === 0) {
      return res.status(404).json({ error: "Asset not found." });
    }
    res.status(200).json(result.rows[0]);
  } catch (error) {
    console.error("Error fetching asset:", error);
    res.status(500).json({ error: "Failed to fetch asset." });
  }
});

export default router;