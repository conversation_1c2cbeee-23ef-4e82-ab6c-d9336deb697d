import { defineStore } from 'pinia';
import apiClient from '@/services/apiClient';

export const useClientStore = defineStore('client', {
  state: () => ({
    clientCount: null as number | null,
    clients: [] as any[],
    isLoading: false,
    error: null as string | null,
  }),

  getters: {
    effectiveClientCount(state) {
      if (state.clientCount !== null && state.clientCount !== undefined) {
        return state.clientCount;
      } else {
        return 0; // default value
      }
    },
  },

  actions: {
    async fetchClientCount() {
      try {
        this.isLoading = true;
        this.error = null;

        const response = await apiClient.get('/clients');

        if (response.data && response.data.clients) {
          this.clients = response.data.clients;
          this.clientCount = response.data.clients.length;
        } else {
          this.clientCount = 0;
          this.clients = [];
        }

        return this.clientCount;
      } catch (error) {
        console.error('Error fetching client count:', error);
        this.error = 'Failed to fetch client count';
        this.clientCount = 0;
        this.clients = [];
        return 0;
      } finally {
        this.isLoading = false;
      }
    },

    async fetchClients() {
      try {
        this.isLoading = true;
        this.error = null;

        const response = await apiClient.get('/clients');

        if (response.data && response.data.clients) {
          this.clients = response.data.clients;
          this.clientCount = response.data.clients.length;
        } else {
          this.clients = [];
          this.clientCount = 0;
        }

        return this.clients;
      } catch (error) {
        console.error('Error fetching clients:', error);
        this.error = 'Failed to fetch clients';
        this.clients = [];
        this.clientCount = 0;
        return [];
      } finally {
        this.isLoading = false;
      }
    }
  }
});
